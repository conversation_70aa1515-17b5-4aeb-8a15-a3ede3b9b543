import json
import os
import traceback
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

from config_parser import ConfigParser
from simulation_orchestrator import SimulationOrchestrator
from metrics import MetricsCalculator
from history_manager import HistoryManager


app = Flask(__name__)
CORS(app)  # 允许跨域请求

class APIServer:
    def __init__(self):
        self.config_parser = ConfigParser()
        self.simulation_orchestrator = SimulationOrchestrator()
        self.history_manager = HistoryManager()

    def start(self, host='localhost', port=8888, debug=False):
        """启动API服务器"""
        print(f"=== 大型活动仿真后端API ===")
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务地址: http://{host}:{port}")
        print(f"主要接口:")
        print(f"  - 仿真启动: http://{host}:{port}/api/start_simulation")
        print(f"  - 路网文件: http://{host}:{port}/api/get_network_file")
        print(f"  - 配置验证: http://{host}:{port}/api/config/validate")
        print(f"  - 历史方案: http://{host}:{port}/api/history/*")
        print("=" * 50)

        app.run(host=host, port=port, debug=debug)

# 创建全局API服务器实例
api_server = APIServer()

@app.route('/api/start_simulation', methods=['POST'])
def start_simulation():
    """
    启动仿真的API接口

    接收JSON配置，启动仿真并返回结果
    """
    try:
        # 1. 获取JSON配置
        if not request.is_json:
            return jsonify({
                'success': False,
                'error_code': 'INVALID_FORMAT',
                'message': '请求必须是JSON格式',
                'details': {'content_type': request.content_type}
            }), 400

        config_data = request.get_json()
        if not config_data:
            return jsonify({
                'success': False,
                'error_code': 'EMPTY_CONFIG',
                'message': '配置数据不能为空'
            }), 400

        # 2. 验证和解析配置
        validation_result = api_server.config_parser.validate_config(config_data)
        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'error_code': 'INVALID_CONFIG',
                'message': '配置验证失败',
                'details': validation_result['errors']
            }), 400

        # 3. 生成配置摘要
        config_summary = api_server.config_parser.generate_config_summary(config_data)

        # 4. 启动仿真
        print(f"\n=== 启动仿真 ===")
        print(f"配置摘要: {config_summary}")

        sim_id = api_server.simulation_orchestrator.run_simulation(config_data)

        # 5. 计算指标
        print(f"=== 计算指标 ===")
        calculator = MetricsCalculator(sim_id)
        metrics = calculator.calculate_metrics(config_data)

        # 7. 保存结果
        result_data = {
            'simulation_id': sim_id,
            'config': config_data,
            'config_summary': config_summary,
            'start_time': datetime.now().isoformat(),
            'simulation_results': api_server._format_metrics(metrics)
        }

        # 保存详细结果到仿真目录
        sim_dir = os.path.join('sumo_data', sim_id)
        os.makedirs(sim_dir, exist_ok=True)
        result_file = os.path.join(sim_dir, f'simulation_result_{sim_id}.json')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=4)

        print(f"=== 仿真完成 ===")
        print(f"仿真ID: {sim_id}")
        print(f"结果文件: {result_file}")

        # 8. 返回成功响应（符合前端期望的新格式）
        return jsonify({
            'success': True,
            'data': result_data,  # 将完整数据放在data字段下
            'message': '仿真执行成功',
            'result_file': result_file
        }), 200

    except Exception as e:
        error_trace = traceback.format_exc()
        print(f"仿真执行错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")

        return jsonify({
            'success': False,
            'error_code': 'SIMULATION_ERROR',
            'message': f'仿真执行失败: {str(e)}',
            'details': {'trace': error_trace if app.debug else None}
        }), 500

@app.route('/api/config/validate', methods=['POST'])
def validate_config():
    """
    验证配置接口（不执行仿真）
    """
    try:
        if not request.is_json:
            return jsonify({
                'valid': False,
                'error': '请求必须是JSON格式'
            }), 400

        config_data = request.get_json()
        validation_result = api_server.config_parser.validate_config(config_data)

        if validation_result['valid']:
            config_summary = api_server.config_parser.generate_config_summary(config_data)
            return jsonify({
                'valid': True,
                'message': '配置验证通过',
                'config_summary': config_summary
            })
        else:
            return jsonify({
                'valid': False,
                'errors': validation_result['errors']
            }), 400

    except Exception as e:
        return jsonify({
            'valid': False,
            'error': f'验证过程出错: {str(e)}'
        }), 500

@app.route('/api/get_network_file', methods=['POST'])
def get_network_file():
    """
    获取路网文件内容，供前端解析使用
    """
    try:
        data = request.json
        file_path = data.get('file_path')
        
        if not file_path:
            return jsonify({
                'success': False,
                'error': '文件路径不能为空'
            }), 400
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'文件不存在: {file_path}'
            }), 404
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return content, 200, {'Content-Type': 'text/xml; charset=utf-8'}
        
    except Exception as e:
        print(f"获取路网文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'读取文件失败: {str(e)}'
        }), 500

# 添加格式化指标的辅助方法
def _format_metrics(self, metrics):
    """格式化指标数据为标准输出格式"""
    formatted = {}
    
    # 网络级指标（原来的四大指标）
    network_metrics = {
        "pedestrian_metrics": {
            "average_travel_time": 0,
            "average_waiting_time": 0,
            "average_waiting_count": 0,
            "average_time_loss": 0
        },
        "vehicle_metrics": {
            "average_travel_time": 0,
            "average_waiting_time": 0,
            "average_waiting_count": 0,
            "average_time_loss": 0
        },
        "vip_vehicle_metrics": {
            "average_travel_time": 0,
            "average_waiting_time": 0,
            "average_waiting_count": 0,
            "average_time_loss": 0
        },
        "venue_area_metrics": {
            "average_pedestrian_travel_time": 0,
            "average_pedestrian_delay": 0,
            "average_vehicle_travel_time": 0,
            "average_vehicle_delay": 0
        }
    }

    # 填充行人指标
    if 'pedestrian_metrics' in metrics:
        pm = metrics['pedestrian_metrics']
        network_metrics["pedestrian_metrics"] = {
            "average_travel_time": round(pm.get('avg_duration', 0), 2),
            "average_waiting_time": round(pm.get('avg_waitingTime', 0), 2),
            "average_waiting_count": round(pm.get('avg_waitingCount', 0), 2),
            "average_time_loss": round(pm.get('avg_timeLoss', 0), 2)
        }

    # 填充车辆指标
    if 'vehicle_metrics' in metrics:
        vm = metrics['vehicle_metrics']
        network_metrics["vehicle_metrics"] = {
            "average_travel_time": round(vm.get('avg_duration', 0), 2),
            "average_waiting_time": round(vm.get('avg_waiting_time', 0), 2),
            "average_waiting_count": round(vm.get('avg_waiting_count', 0), 2),
            "average_time_loss": round(vm.get('avg_time_loss', 0), 2)
        }

    # 填充VIP指标
    if 'vip_metrics' in metrics:
        vip = metrics['vip_metrics']
        network_metrics["vip_vehicle_metrics"] = {
            "average_travel_time": round(vip.get('vip_avg_duration', 0), 2),
            "average_waiting_time": round(vip.get('vip_avg_waiting_time', 0), 2),
            "average_waiting_count": round(vip.get('vip_avg_waiting_count', 0), 2),
            "average_time_loss": round(vip.get('vip_avg_time_loss', 0), 2)
        }

    # 填充场馆指标
    if 'venue_metrics' in metrics:
        venue = metrics['venue_metrics']
        network_metrics["venue_area_metrics"] = {
            "average_vehicle_travel_time": round(venue.get('vehicle_avg_traveltime', 0), 2),
            "average_vehicle_delay": round(venue.get('vehicle_avg_timeloss', 0), 2),
            "average_pedestrian_travel_time": round(venue.get('people_avg_traveltime', 0), 2),
            "average_pedestrian_delay": round(venue.get('people_avg_timeloss', 0), 2)
        }
    
    # 将网络级指标放在network_metrics下
    formatted["network_metrics"] = network_metrics
    
    # 添加路段指标（如果存在的话，原样传递）
    if 'selected_edge_metrics' in metrics:
        formatted["selected_edge_metrics"] = metrics['selected_edge_metrics']
    
    return formatted

# 将格式化方法绑定到APIServer类
APIServer._format_metrics = _format_metrics

# ==================== 历史方案管理API ====================

@app.route('/api/history/save', methods=['POST'])
def save_scheme():
    """保存配置方案"""
    try:
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式'
            }), 400

        data = request.get_json()
        config = data.get('config')
        name = data.get('name')
        description = data.get('description')
        simulation_results = data.get('simulation_results')   # 新增

        if not config:
            return jsonify({
                'success': False,
                'error': '配置数据不能为空'
            }), 400

        # 验证配置格式
        validation_result = api_server.config_parser.validate_config(config)
        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'error': '配置格式无效',
                'details': validation_result['errors']
            }), 400

        # 保存方案（加上 simulation_results）
        result = api_server.history_manager.save_scheme(config, name, description, simulation_results)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'保存方案时发生错误: {str(e)}'
        }), 500


@app.route('/api/history/list', methods=['GET'])
def get_schemes_list():
    """获取历史方案列表"""
    try:
        schemes = api_server.history_manager.get_schemes_list()
        return jsonify({
            'success': True,
            'schemes': schemes,
            'count': len(schemes)
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取方案列表时发生错误: {str(e)}'
        }), 500

@app.route('/api/history/get/<scheme_id>', methods=['GET'])
def get_scheme_detail(scheme_id):
    """获取方案详情"""
    try:
        scheme = api_server.history_manager.get_scheme_by_id(scheme_id)

        if scheme:
            return jsonify({
                'success': True,
                'scheme': scheme
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': f'未找到ID为 {scheme_id} 的方案'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取方案详情时发生错误: {str(e)}'
        }), 500

@app.route('/api/history/delete/<scheme_id>', methods=['DELETE'])
def delete_scheme(scheme_id):
    """删除方案"""
    try:
        result = api_server.history_manager.delete_scheme(scheme_id)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'删除方案时发生错误: {str(e)}'
        }), 500

@app.route('/api/history/update/<scheme_id>', methods=['PUT'])
def update_scheme_info(scheme_id):
    """更新方案信息"""
    try:
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式'
            }), 400

        data = request.get_json()
        name = data.get('name')
        description = data.get('description')

        result = api_server.history_manager.update_scheme_info(scheme_id, name, description)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新方案信息时发生错误: {str(e)}'
        }), 500

# ==================== 地图加载API ====================

@app.route('/api/background-image', methods=['GET'])
def get_background_image():
    """获取底图信息"""
    return jsonify({
        "image": "gym_large.jpg",
        "centerX": 5520.00,
        "centerY": 2710.00,
        "width": 9800.00,
        "height": 5400.00,
        "rotation": 0
    })

@app.route('/api/network', methods=['GET'])
def get_network():
    """获取网络数据"""
    try:
        network_data = api_server.simulation_orchestrator.get_network_geojson()
        return jsonify(network_data), 200
    except Exception as e:
        return jsonify({
            'error': f'获取路网数据失败: {str(e)}'
        }), 500

# ==================== 仿真回放数据API ====================

@app.route('/api/simulation/<sim_id>/replay_data', methods=['GET'])
def get_simulation_replay_data(sim_id):
    """获取仿真回放数据"""
    try:
        frames_file = os.path.join('sumo_data', sim_id, 'simulation_frames.json')

        if not os.path.exists(frames_file):
            return jsonify({
                'error': f'仿真 {sim_id} 的回放数据文件不存在'
            }), 404

        with open(frames_file, 'r', encoding='utf-8') as f:
            simulation_data = json.load(f)

        return jsonify(simulation_data), 200

    except Exception as e:
        return jsonify({
            'error': f'获取仿真回放数据失败: {str(e)}'
        }), 500

# 提供底图文件访问
@app.route('/files/<path:filename>')
def serve_static_file(filename):
    """提供静态文件访问"""
    
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    static_dir = os.path.join(current_dir, 'sumo_data', 'templates')

    return send_from_directory(static_dir, filename)

if __name__ == '__main__':
    # 直接启动服务器
    api_server.start(debug=True)
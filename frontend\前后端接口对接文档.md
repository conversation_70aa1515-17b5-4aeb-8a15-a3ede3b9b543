# 前后端接口对接文档

## 概述

本文档详细说明前端与后端仿真系统的接口规范，包括JSON数据格式、API调用方式、错误处理等。

**后端服务地址**: `http://localhost:8888`  
**API版本**: v1.0  
**数据格式**: JSON  
**字符编码**: UTF-8

---

## 1. API接口规范
### 1.1 配置验证接口

用于验证配置JSON的有效性，不执行仿真。

```http
POST /api/config/validate
Content-Type: application/json
```

**请求体**: 完整的配置JSON（格式见下文）

**成功响应**:
```json
{
  "valid": true,
  "message": "配置验证通过",
  "config_summary": {
    "network": "预设路网 - 仅开放东侧出入口",
    "signal": "预设配时",
    "traffic": "预设需求 - 进场场景"
  }
}
```

**失败响应** (HTTP 400):
```json
{
  "valid": false,
  "errors": [
    {
      "module": "traffic_config",
      "field": "vip_priority.enabled",
      "error": "VIP优先通行需要车辆类型为'存在贵宾专车'"
    }
  ]
}
```

**前端调用示例**:
```javascript
async function validateConfig(config) {
    try {
        const response = await fetch('http://localhost:8888/api/config/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });
        
        const result = await response.json();
        
        if (result.valid) {
            console.log('配置验证通过:', result.config_summary);
            return { valid: true, summary: result.config_summary };
        } else {
            console.error('配置验证失败:', result.errors);
            return { valid: false, errors: result.errors };
        }
    } catch (error) {
        console.error('验证请求失败:', error);
        return { valid: false, errors: [{ error: '网络错误' }] };
    }
}
```

### 1.2 网络选择器接口

用于启动可视化网络选择器，让用户在地图上选择交叉口或路段。

```http
POST /api/network/select
Content-Type: application/json
Timeout: 300秒
```

**请求体**:
```json
{
  "net_file_path": "sumo_data/templates/gym_tls.net.xml",
  "selector_type": "intersection"
}
```

**参数说明**:
- `net_file_path` (必填): 路网文件路径，必须是有效的.net.xml文件
- `selector_type` (必填): 选择器类型
  - `intersection` - 启动交叉口选择器（用于信号优化）
  - `edge` - 启动路段选择器（用于道路限行）

**成功响应**:
```json
{
  "success": true,
  "selected_ids": [
    "10005414142", 
    "10309271185"
  ],
  "selector_type": "intersection",
  "count": 2
}
```

**失败响应** (HTTP 400/404):
```json
{
  "success": false,
  "error": "路网文件不存在: /invalid/path.net.xml"
}
```

**前端调用示例**:
```javascript
// 交叉口选择（信号优化）
async function selectIntersections(netFilePath) {
    try {
        showLoading('正在启动交叉口选择器...');
        
        const response = await fetch('http://localhost:8888/api/network/select', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                net_file_path: netFilePath,
                selector_type: 'intersection'
            }),
            timeout: 300000  // 5分钟超时，等待用户选择
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            console.log(`用户选择了 ${result.count} 个交叉口:`, result.selected_ids);
            // 自动填写到配置表单
            updateIntersectionSelection(result.selected_ids);
            return result.selected_ids;
        } else {
            showError(result.error);
            return [];
        }
    } catch (error) {
        hideLoading();
        console.error('选择器启动失败:', error);
        return [];
    }
}

// 路段选择（道路限行）
async function selectRestrictedEdges(netFilePath) {
    try {
        showLoading('正在启动路段选择器...');
        
        const response = await fetch('http://localhost:8888/api/network/select', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                net_file_path: netFilePath,
                selector_type: 'edge'
            }),
            timeout: 300000
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            console.log(`用户选择了 ${result.count} 个限行路段:`, result.selected_ids);
            // 自动填写到配置表单
            updateEdgeSelection(result.selected_ids);
            return result.selected_ids;
        } else {
            showError(result.error);
            return [];
        }
    } catch (error) {
        hideLoading();
        console.error('选择器启动失败:', error);
        return [];
    }
}
```

**使用流程**:
1. 用户在前端界面点击"选择交叉口"或"选择路段"按钮
2. 前端调用 `/api/network/select` 接口
3. 系统自动打开浏览器窗口，显示路网地图
4. 用户在地图上点击选择相应元素（蓝色圆圈=交叉口，灰色线条=路段）
5. 用户点击"确认选择"完成选择
6. 选择器返回用户选择的ID列表给前端
7. 前端自动将选择结果填写到配置表单中

### 1.3 启动仿真接口

主要的仿真启动接口，执行完整的仿真流程。

```http
POST /api/start_simulation
Content-Type: application/json
Timeout: 300秒
```

**请求体**: 完整的配置JSON

**成功响应**:
```json
{
  "success": true,
  "simulation_id": "250629163000",
  "message": "仿真执行成功",
  "config_summary": {
    "network": "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
    "signal": "预设配时 + 自定义优化(4个交叉口)",
    "traffic": "预设需求 - 进场场景 + 贵宾专车"
  },
  "metrics": {
    "pedestrian_metrics": {
      "average_travel_time": 123.45,
      "average_waiting_time": 12.34,
      "average_waiting_count": 2.1,
      "average_time_loss": 23.45
    },
    "vehicle_metrics": {
      "average_travel_time": 234.56,
      "average_waiting_time": 45.67,
      "average_waiting_count": 3.2,
      "average_time_loss": 67.89
    },
    "vip_vehicle_metrics": {
      "average_travel_time": 145.23,
      "average_waiting_time": 8.91,
      "average_waiting_count": 1.2,
      "average_time_loss": 12.34
    },
    "venue_area_metrics": {
      "average_pedestrian_travel_time": 156.78,
      "average_pedestrian_delay": 34.56,
      "average_vehicle_travel_time": 267.89,
      "average_vehicle_delay": 78.90
    }
  },
  "result_file": "simulation_result_250629163000.json"
}
```

**失败响应** (HTTP 400/500):
```json
{
  "success": false,
  "error_code": "SIMULATION_ERROR",
  "message": "仿真执行失败: SUMO启动失败",
  "details": {
    "trace": "详细错误堆栈信息..."
  }
}
```

**前端调用示例**:
```javascript
async function startSimulation(config) {
    try {
        // 显示加载状态
        showLoading('正在启动仿真，请稍候...');
        
        const response = await fetch('http://localhost:8888/api/start_simulation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config),
            timeout: 300000  // 5分钟超时
        });
        
        const result = await response.json();
        
        hideLoading();
        
        if (result.success) {
            console.log('仿真启动成功:', result.simulation_id);
            displaySimulationResults(result);
            return { success: true, data: result };
        } else {
            console.error('仿真失败:', result.message);
            displayError(result);
            return { success: false, error: result };
        }
        
    } catch (error) {
        hideLoading();
        console.error('仿真请求失败:', error);
        return { success: false, error: { message: '网络错误或超时' } };
    }
}
```

---

## 2. 配置JSON格式

### 2.1 配置格式说明

⚠️ **配置JSON的详细格式规范请参考**: [`配置模块文档.md`](配置模块文档.md)

配置JSON包含三个主要模块：
- `network_config` - 仿真路网配置
- `signal_config` - 信号配时配置  
- `traffic_config` - 交通需求配置

### 2.2 快速配置示例

#### 基础配置
```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": { "enabled": false, "restricted_edges": [] }
  },
  "signal_config": {
    "type": "predefined", 
    "file_path": null,
    "optimization": { "enabled": false, "selected_intersections": [] }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "进场",
    "vehicle_type": "仅一般车辆",
    "vip_priority": { "enabled": false }
  }
}
```

#### 高级配置（含网络选择）
```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": {
      "enabled": true,
      "restricted_edges": ["edge_123", "edge_456"]  // 通过网络选择器获得
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": true,
      "selected_intersections": ["10005414142", "10007624522"]  // 通过网络选择器获得
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "离场",
    "vehicle_type": "存在贵宾专车",
    "vip_priority": { "enabled": true }
  }
}
```

---

## 3. 前端集成流程

### 3.1 完整的前端集成示例

```javascript
class SimulationController {
    constructor() {
        this.apiBase = 'http://localhost:8888';
        this.currentConfig = this.getDefaultConfig();
    }
    
    // 获取默认配置
    getDefaultConfig() {
        return {
            network_config: {
                type: "predefined",
                file_path: null,
                entrance_plan: "仅开放东侧出入口",
                road_restriction: { enabled: false, restricted_edges: [] }
            },
            signal_config: {
                type: "predefined",
                file_path: null,
                optimization: { enabled: false, selected_intersections: [] }
            },
            traffic_config: {
                type: "predefined",
                file_path: null,
                scenario: "进场",
                vehicle_type: "仅一般车辆",
                vip_priority: { enabled: false }
            }
        };
    }
    
    // 启动交叉口选择器
    async selectIntersections() {
        const selectedIds = await this.callNetworkSelector('intersection');
        if (selectedIds.length > 0) {
            this.currentConfig.signal_config.optimization.enabled = true;
            this.currentConfig.signal_config.optimization.selected_intersections = selectedIds;
            this.updateUI('intersections', selectedIds);
        }
    }
    
    // 启动路段选择器
    async selectRestrictedEdges() {
        const selectedIds = await this.callNetworkSelector('edge');
        if (selectedIds.length > 0) {
            this.currentConfig.network_config.road_restriction.enabled = true;
            this.currentConfig.network_config.road_restriction.restricted_edges = selectedIds;
            this.updateUI('edges', selectedIds);
        }
    }
    
    // 调用网络选择器
    async callNetworkSelector(type) {
        try {
            const response = await fetch(`${this.apiBase}/api/network/select`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    net_file_path: 'sumo_data/templates/gym_tls.net.xml',
                    selector_type: type
                }),
                timeout: 300000
            });
            
            const result = await response.json();
            return result.success ? result.selected_ids : [];
        } catch (error) {
            console.error('选择器启动失败:', error);
            return [];
        }
    }
    
    // 验证配置
    async validateConfiguration() {
        try {
            const response = await fetch(`${this.apiBase}/api/config/validate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(this.currentConfig)
            });
            
            const result = await response.json();
            if (result.valid) {
                this.showSuccess('配置验证通过');
                return true;
            } else {
                this.showErrors(result.errors);
                return false;
            }
        } catch (error) {
            this.showError('配置验证失败');
            return false;
        }
    }
    
    // 启动仿真
    async startSimulation() {
        if (!await this.validateConfiguration()) {
            return;
        }
        
        try {
            this.showLoading('正在启动仿真...');
            
            const response = await fetch(`${this.apiBase}/api/start_simulation`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(this.currentConfig),
                timeout: 300000
            });
            
            const result = await response.json();
            this.hideLoading();
            
            if (result.success) {
                this.displayResults(result);
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            this.hideLoading();
            this.showError('仿真启动失败');
        }
    }
}
```